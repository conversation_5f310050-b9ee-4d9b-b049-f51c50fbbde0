<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>MindAR AR Responsive</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />

    <script src="https://aframe.io/releases/1.5.0/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/donmccurdy/aframe-extras@v7.0.0/dist/aframe-extras.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mind-ar@1.2.5/dist/mindar-image-aframe.prod.js"></script>

    <style>
      html, body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        background: black;
        position: fixed;
        top: 0;
        left: 0;
      }

      a-scene {
        width: 100% !important;
        height: 100% !important;
        position: fixed !important;
        top: 0;
        left: 0;
        margin: 0;
        padding: 0;
      }

      .a-enter-vr {
        display: none !important;
      }

      .control-button, .google-button {
        position: absolute;
        z-index: 20;
        font-weight: bold;
        cursor: pointer;
        transform-origin: center;
        transition: all 0.3s ease;
      }

      .control-button {
        background-color: rgba(255, 255, 255, 0.7);
        border-radius: 5px;
        padding: 8px 12px;
        display: none;
        border: none;
        font-size: 14px;
      }

      .animate-button {
        top: 10px;
        right: 10px;
      }

      .info-button {
        top: 10px;
        right: 120px;
      }

      .control-button:hover {
        background-color: rgba(255, 255, 255, 0.9);
        transform: scale(1.05);
      }

      .control-button:active {
        transform: scale(0.95);
      }

      .control-button.animate {
        animation: buttonPulse 0.6s ease-in-out;
      }

      @keyframes buttonPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); background-color: rgba(76, 175, 80, 0.8); }
        100% { transform: scale(1); }
      }

      .google-button {
        background-color: #4285F4;
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: none;
        font-size: 16px;
        font-weight: bold;
        z-index: 5;
      }

      .google-button:hover {
        background-color: #3367D6;
        transform: translate(-50%, -50%) scale(1.1);
      }
    </style>
  </head>

  <body>
    <a-scene
      mindar-image="imageTargetSrc: https://FAiRarWeb.github.io/project123/targets.mind;
                    uiScanning: true;
                    filterMinCF: 0.0005;
                    filterBeta: 0.01;
                    missTolerance: 3;
                    warmupTolerance: 2;"
      vr-mode-ui="enabled: false"
      device-orientation-permission-ui="enabled: false"
      renderer="precision: high; antialias: true"
      embedded
    >
      <a-assets>
        <video id="video1" src="https://FAiRarWeb.github.io/project123/videoAR.mp4" preload="auto" loop crossorigin="anonymous"></video>
      </a-assets>

      <a-camera position="0 0 0" look-controls="enabled: false"></a-camera>

      <a-entity mindar-image-target="targetIndex: 0" videohandler>
        <a-video
          src="#video1"
          position="0 0 0.01"
          width="1"
          height="0.45"
          autoplay="false"
          loop="true"
          visible="false"
        ></a-video>

        <!-- Info button as A-Frame entity behind the video -->
        <a-plane
          id="infoButtonAR"
          position="0 0 -0.01"
          width="0.15"
          height="0.15"
          color="#4285F4"
          visible="false"
          class="clickable"
        >
          <a-text
            value="i"
            position="0 0 0.001"
            align="center"
            color="white"
            font="kelsonsans"
            width="8"
          ></a-text>
        </a-plane>
      </a-entity>
    </a-scene>

    <button id="animateButton" class="control-button animate-button">Animate Me</button>
    <button id="infoButton" class="control-button info-button">Info</button>
    <button id="googleButton" class="google-button" onclick="window.open('https://www.google.com', '_blank')">i</button>

    <script>
      AFRAME.registerComponent("videohandler", {
        init: function () {
          const videoEl = document.querySelector("#video1");
          const videoPlane = this.el.querySelector("a-video");
          const animateButton = document.querySelector("#animateButton");
          const infoButton = document.querySelector("#infoButton");
          const googleButton = document.querySelector("#googleButton");
          const infoButtonAR = this.el.querySelector("#infoButtonAR");
          const camera = document.querySelector("a-camera").object3D;

          // Track states
          let deviceOrientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
          let isAnimatePressed = false;
          let isTargetFound = false;
          
          // Device orientation detection
          window.addEventListener("deviceorientation", (event) => {
            // Get gamma (rotation around front-to-back axis)
            const gamma = Math.abs(event.gamma);
            
            // Determine orientation based on gamma angle
            const newOrientation = (gamma > 45 && gamma < 135) ? 'landscape' : 'portrait';
            
            // Only update if orientation changed
            if (newOrientation !== deviceOrientation) {
              deviceOrientation = newOrientation;
              updateLayout(deviceOrientation);
            }
          }, true);
          
          const updateLayout = (orientation) => {
            console.log("Device orientation:", orientation);
            
            if (orientation === 'landscape') {
              // Keep video in original orientation (no rotation)
              videoPlane.setAttribute("rotation", "0 0 0");

              // Only rotate UI buttons
              animateButton.style.transform = "rotate(90deg)";
              infoButton.style.transform = "rotate(90deg)";
              googleButton.style.transform = "rotate(90deg)";
            } else {
              // Keep original rotation in portrait mode
              videoPlane.setAttribute("rotation", "0 0 0");

              // Reset UI buttons rotation
              animateButton.style.transform = "rotate(0deg)";
              infoButton.style.transform = "rotate(0deg)";
              googleButton.style.transform = "translate(-50%, -50%) scale(1)";
            }
          };
          
          // Initialize layout based on current orientation
          updateLayout(deviceOrientation);
          
          const checkAndPlayVideo = () => {
            if (isAnimatePressed && isTargetFound) {
              console.log("Both conditions met - playing video");
              videoPlane.setAttribute("visible", true);
              videoEl.play().catch(e => console.warn("Autoplay blocked:", e));
            } else {
              videoPlane.setAttribute("visible", false);
              videoEl.pause();
            }
          };

          this.el.addEventListener("targetFound", () => {
            console.log("Target found");
            isTargetFound = true;
            animateButton.style.display = "block";
            infoButton.style.display = "block";

            // Apply current orientation when target is found
            updateLayout(deviceOrientation);

            // Check if we should play video
            checkAndPlayVideo();
          });

          this.el.addEventListener("targetLost", () => {
            console.log("Target lost");
            isTargetFound = false;
            videoEl.pause();
            videoPlane.setAttribute("visible", false);
            animateButton.style.display = "none";
            infoButton.style.display = "none";
            googleButton.style.display = "none";
            infoButtonAR.setAttribute("visible", false);

            // Reset button state when target is lost
            isAnimatePressed = false;
          });

          // Animate Me button - controls video playback
          animateButton.addEventListener("click", () => {
            // Add animation class
            animateButton.classList.add("animate");

            // Remove animation class after animation completes
            setTimeout(() => {
              animateButton.classList.remove("animate");
            }, 600);

            // Toggle video playback
            isAnimatePressed = !isAnimatePressed;
            checkAndPlayVideo();

            console.log("Animate button pressed, isAnimatePressed:", isAnimatePressed);
          });

          // Info button - controls "i" button visibility
          infoButton.addEventListener("click", () => {
            // Add animation class
            infoButton.classList.add("animate");

            // Remove animation class after animation completes
            setTimeout(() => {
              infoButton.classList.remove("animate");
            }, 600);

            // Toggle AR info button visibility
            const isHidden = !infoButtonAR.getAttribute("visible");
            infoButtonAR.setAttribute("visible", isHidden);

            console.log("Info button pressed, AR button visible:", isHidden);
          });

          // Add click functionality to the AR info button
          infoButtonAR.addEventListener("click", () => {
            window.open('https://www.google.com', '_blank');
          });

          this.tick = () => {
            if (googleButton.style.display !== "block" || !isTargetFound) return;

            // Position button on the video plane (behind the video)
            const videoWorldPosition = new THREE.Vector3();
            videoPlane.object3D.getWorldPosition(videoWorldPosition);

            const vector = videoWorldPosition.clone();
            vector.project(camera.children[0]);

            const widthHalf = window.innerWidth / 2;
            const heightHalf = window.innerHeight / 2;
            const x = (vector.x * widthHalf) + widthHalf;
            const y = -(vector.y * heightHalf) + heightHalf;

            const distance = camera.position.distanceTo(videoWorldPosition);
            const scaleFactor = Math.max(0.5, Math.min(1.5, 1 / Math.max(0.1, distance)));

            googleButton.style.left = `${x}px`;
            googleButton.style.top = `${y}px`;

            // Apply rotation based on current orientation along with scaling
            const rotation = deviceOrientation === 'landscape' ? 'rotate(90deg)' : 'rotate(0deg)';
            googleButton.style.transform = `translate(-50%, -50%) scale(${scaleFactor}) ${rotation}`;
          };
        },
      });
    </script>
  </body>
</html>
